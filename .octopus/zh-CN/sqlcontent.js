export default {
  selecttable: {
    xuanZeBiao: '选择表',
    weiZhuCe: '未注册',
    yiZhuCe: '已注册',
    biaoMingCheng: '表名称',
  },
  sqljob: {
    ceShiZhong: '测试中',
    chuangJianFenQuBiao:
      "--创建分区表\ncreate table default.tab_test_dt (\n  id  int  comment  'id',\n  name string comment  '姓名')\ncomment   '全部数据类型表'\npartitioned by (\n  ds  string  comment  '分区字段'\n);\n\n--创建非分区表\ncreate table default.tab_test_dt (\n  id  int  comment  'id',\n  name string comment  '姓名')\ncomment   '全部数据类型表';\n\n--向分区表插入数据\ninsert into salaxy.tab_test_dt partition(ds='201709') values(1,'wangwu');\n\n--向非分区表插入数据\ninsert into salaxy.tab_test_dt values(1,'wangwu');\n\n-- 查询语句\nselect * from default.tab_test_dt limit 10;\n\n-- 查询语句(使用参数）\nselect * from default.tab_test_dt where ds = ${bizdate} limit 10;（例如参数配置为bizdate=$[yyyyMMdd-1d]）",
  },
  tablesource: {
    xuanZeBiao: '选择表',
    fenQu: '分区',
  },
  index: {
    qingXianXuanZeShu: '请先选择数据源',
  },
};
