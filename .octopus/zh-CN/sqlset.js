export default {
  params: {
    tianJiaCanShuPei: '添加参数配置',
    sanBianLiangXuYao: '三、变量需要选择系统字段并设置默认值。',
    jiaHaoHuoZheJian:
      '3、加号或者减号，表示在当前时间上加减操作，例如：bizdate=$[yyyyMMdd-1d]，表示昨天，今天为20200115，计算结果值为20200114',
    shiJianLeiXingY: '2、时间类型：y年，M月，d天，h小时，m分钟，s秒',
    shiJianGeShiFu: '1、时间格式符号：yyyy年，MM月，dd天，HH小时，mm分钟，ss秒',
    erShiJianBiaoDa:
      '二、时间表达式格式：$[时间格式(加号或减号 整数值+时间类型)]，小括号后面的可以省略',
    yiChangLiangBuXu: '一、常量不需要引号，例如：',
    canShuPeiZhi: '参数配置',
    qingShuRuBianLiang: '请输入变量默认值',
    qingShuRuCanShu: '请输入参数配置',
    shiJianBiaoDaShi: '时间表达式',
  },
  sqlquery: {
    zhiChiShuRuYi:
      '支持输入一条select语句，且使用到的表都必须在当前选择的数据源下。\nsql语句不可设置limit，默认返回前100条数据。',
    chaXunYuJuS:
      '--查询语句\nselect * from default.tab_test_dt\n \n--查询语句(使用参数）\nselect * from default.tab_test_dt where ds = ${bizdate}\n（例如配置了时间表达式参数为bizdate=$[yyyyMMdd-1d]）\n \n--Presto时间查询语句\nselect * from default.tabtestdt where entry_time>timestamp 2023-01-01 00:00:00;',
    qingShuRuSQ: '请输入sql内容',
    canShuPeiZhi: '参数配置',
    zhiChiYouShuZi:
      '支持由数字、英文、中文、下划线、空格、横线组成，且不能以下划线开头',
  },
};
