export default {
  params: {
    tianJiaCanShuPei: 'Add Parameter Configuration',
    sanBianLiangXuYao:
      'III. Variables need to select system fields and set default values.',
    jiaHaoHuoZheJian:
      '3. Plus or minus signs indicate addition or subtraction operations on the current time. For example: bizdate=$[yyyyMMdd-1d], which means yesterday. If today is 20200115, the calculated result value is 20200114.',
    shiJianLeiXingY:
      '2. Time types: y for year, M for month, d for day, h for hour, m for minute, s for second.',
    shiJianGeShiFu:
      'Time format symbols: yyyy for year, MM for month, dd for day, HH for hour, mm for minute, ss for second.',
    erShiJianBiaoDa:
      'II. Time expression format: $[Time format (plus or minus integer value + time type)], and what follows the parentheses can be omitted.',
    yiChangLiangBuXu: 'I. Constants do not need quotation marks. For example:',
    canShuPeiZhi: 'Parameter Configuration',
    qingShuRuBianLiang: 'Please enter the default value of the variable',
    qingShuRuCanShu: 'Please enter the parameter configuration',
    shiJianBiaoDaShi: 'Time Expression',
  },
  sqlquery: {
    zhiChiShuRuYi:
      '"Support entering a single select statement, and all the tables used must be under the currently selected data source.\nThe SQL statement cannot set the LIMIT clause, and the first 100 records will be returned by default."',
    chaXunYuJuS:
      '"-- Query statement\nselect * from default.tab_test_dt\n-- Query statement (using parameters)\nselect * from default.tab_test_dt where ds = [yyyyMMdd-1d])\n-- Presto time query statement\nselect * from default.tabtestdt where entry_time>timestamp 2023-01-01 00:00:00;"',
    qingShuRuSQ: 'Please enter the SQL content',
    canShuPeiZhi: 'Parameter Configuration',
    zhiChiYouShuZi:
      'It can be composed of numbers, English letters, Chinese characters, underscores, spaces, and hyphens, and cannot start with an underscore.',
  },
};
