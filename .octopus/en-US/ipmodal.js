export default {
  index: {
    iPSheZhi<PERSON>hi:
      '2. IP settings support single IP settings or range IP settings, separated by semicolons. For example: ***********; *********** - *************.',
    iPBaiMingDan:
      'After the IP whitelist is set, the service interface can only be accessed by the set IPs.',
    qingShuRuIP: 'Please enter the IP whitelist',
    tongYiIPBu: 'The same IP cannot be set in two lists',
    iPShuRuBu: 'The IP input is illegal',
    iPBaiMingDan2: 'IP Whitelist',
    iPHeiMingDan:
      'After the IP blacklist is set, the set IPs will be restricted from accessing the service interface.',
    qingShuRuIP2: 'Please enter the IP blacklist',
    iPHeiMingDan2: 'IP Blacklist',
    iPMingDanShe: 'IP List Setting',
  },
};
