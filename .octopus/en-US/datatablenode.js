export default {
  inmaptable: {
    ruCanZiDuanBiao: 'Input Parameter Field Identifier',
  },
  outmaptable: {
    yingSheXiTongZi: 'Mapping System Field',
    qingXuanZeChuCan: 'Please select the output parameter field identifier',
    chuCanZiDuanBiao: 'Output Parameter Field Identifier',
  },
  outmaptablenew: {
    yingSheXiTongZi: 'Mapping System Field',
    qingXuanZeChuCan: 'Please select the output parameter field identifier',
    chuCanZiDuanBiao: 'Output Parameter Field Identifier',
    tiaoShuJu: 'Records',
    qian: 'Before',
    diYiTiaoShuJu: 'The first record',
  },
  index: {
    qingTianXieZiDuan: 'Please fill in the field',
    xiTongZiDuanBu: 'System field cannot be empty',
    chuCanZiDuanBiao: 'Output parameter field identifier cannot be empty',
    chuCanZiDuanBu: 'Output parameter cannot be empty',
    zhiXingTiaoJianYou: 'There are unfilled items in the execution conditions',
    qingXuanZeShuJu: 'Please select the data table',
    dongTaiBiao: 'Dynamic Table',
    zhuCeBiao: 'Registration Table',
    xuanZeShuJuBiao: 'Select the data table',
    sQLJiaoBen: 'SQL Script',
    qingXuanZeShuJu2: 'Please select the data source',
    xuanZeShuJuYuan: 'Select the data source',
    zhiXingTiaoJianTian: 'The execution conditions are incompletely filled',
    ruCanCeShiWei:
      'The input parameter test has not passed. Please complete the test first.',
    dongTaiBiaoMing: 'Dynamic table name',
  },
};
