export default {
  globalanalysis: {
    diaoYongLiangQuShi: 'Trend chart of call volume',
    yiChangZhiTO: 'Outliers Top 30',
    zhiBianLiangTO: '0-value variables Top 30',
    jinRiDiaoYongLiang: "Today's Call Volume",
    zhiBiaoBianLiang: 'Indicator variable',
    diaoYongLiang: 'Call volume',
    zhanBiZhanBianLiang: 'Percentage (of total calls to variables)',
    yiChangTiaoShu: 'Number of exceptions',
    bianLiangMingCheng: 'Variable name',
    paiMing: 'Ranking',
  },
  indexquality: {
    zuiDaZhi: 'Maximum value',
    fenWeiDian: '99% loci',
    fenWeiDian2: '95% loci',
    pingJunZhi: 'Average value',
    fenWeiDian3: '50% loci',
    fenWeiDian4: '5% loci',
    fenWeiDian5: '1% locus',
    zuiXiaoZhi: 'Minimum value',
    buBaoHanYiChang: 'Excluding outliers and excluding 0',
    buBaoHanYiChang2: 'Excluding outliers',
    baoHanYiChangZhi: 'Including outliers and 0',
    zongShuBuBaoHan: 'Total (excluding outliers and excluding 0)/Total',
    zongShuBuBaoHan2: 'Total (excluding outliers)/Total',
    biLi: 'Proportion',
    biaoZhunChaBuBao: 'Standard deviation (excluding outliers and excluding 0)',
    biaoZhunChaBuBao2: 'Standard deviation (excluding outliers)',
    zongShuBuBaoHan3: 'Total (excluding outliers and excluding 0)',
    zongShuBuBaoHan4: 'Total (excluding outliers)',
    zongShu: 'Aggregate',
    shuZhi: 'Numerical',
    souSuo: 'Search',
    shiJian: 'Time',
    qingXuanZeZhiBiao: 'Select the name of the indicator',
    qingXuanZeTongJi: 'Select the time of the statistics!',
    qingXuanZeZhiBiao2: 'Select the indicator!',
  },
  monitoringanalyses: {
    souSuo: 'Search',
    shiJian: 'Time:',
    qingXuanZeZhiBiao: 'Select an indicator category',
    zhiBiaoFenLei: 'Indicator category:',
    fenLeiZhiBiaoTiao:
      'Trends in the volume of calls for disaggregated Indicators',
    qingXuanZeTongJi: 'Select the time of the statistics!',
    qingXuanZeZhiBiao2: 'Select an indicator classification!',
    diaoYongLiang: 'Call quantity',
    qingXuanZeYiChang: 'Select an outlier',
    yiChangZhiZhanBi: 'Percentage of outliers:',
    suoYouZhiBiaoYi: 'List of outliers for all indicators',
    qingXuanZeYiChang2: 'Select an outlier!',
    yiChangZhiZhanBi2: 'Percentage of outliers',
    zhiBiaoBiaoZhi: 'Indicator ID',
    zhiBiaoMingCheng: 'Indicator name',
    pSIGT: 'PSI > 0.25 List of Variables',
    gTPSI: '0.25 > PSI > 0.1 List of Variables',
    pSILT: 'PSI < 0.1 List of variables',
    qingXuanZePS: 'Select the list of PSI variables',
    pSIJianCe: 'PSI monitoring.',
    suoYouZhiBiaoP: 'List of all indicators PSI',
    qingXuanZePS2: 'Select the PSI variable!',
    zhiBiaoPSI: 'Indicator PSI',
  },
  commontable: {
    peiZhiZhiBiao: 'Configuration indicator',
    yiXuanZhiBiao: 'Selected indicator',
    zhiBiaoPeiZhi: 'Indicator configuration',
    zhiBiaoJiGuanLi: 'Indicator set management',
    gongTOTA: 'Totally {val1} Record(s)',
    caoZuoChengGong: 'The operation was successful.',
    zanWuGengDuo: 'No more',
    shanChu: 'Delete',
    quXiao: 'Cancel',
    queRen: 'OK',
    shanChuQueRen: 'Delete Confirmation',
    zhiBiaoGuanLi: 'Indicator management',
    shangXian: 'Online',
    xiangQing: 'Details',
    banBenZhuangTai: 'Version Status:',
    banBenHao: 'Version Number:',
    bianJi: 'Edit',
    chuangJianBanBen: 'Create version',
    caoZuo: 'Action',
    miaoShu: 'Description',
    xiTongNeiZhi: 'Built in system',
    suoShuQuDao: 'Channel',
    suoShuBaoWen: 'Affiliated message',
    zhiBiaoJiBiaoZhi: 'Identification',
    zhiBiaoJiMingCheng: 'Name',
    xiaXian: 'Offline',
    yiXiaXian: 'Offline',
    yiShangXian: 'Online',
    daoRuDaiShangXian: 'Imported',
    daiShangXian: 'Pending online',
    ceShi: 'Test',
    xiuGai: 'Modify',
    chaKan: 'View',
    fuZhi: 'Copy',
    xiuGaiRen: 'Modifier',
    xiuGaiShiJian: 'Modification date',
    chuangJianRen: 'Creator',
    chuangJianShiJian: 'Creat time',
    ziDingYiZhiBiao: 'Customize indicator',
    muBanZhiBiao: 'Template indicator',
    quZhiZhiBiao: 'Taking value Indicator',
    quanBu: 'All',
    zhiBiaoLeiXing: 'Type',
    zhiBiaoFenLei: 'Category',
    zhiBiaoBiaoZhi: 'Identification',
    zhiBiaoMingCheng: 'Name',
    zhiBiaoJiSuoYin: '(Indicator set) cited, is the deletion confirmed?',
    wenXinTiShiDang: 'Warm tip: The current indicator is being',
    ninQueRenShanChu: 'Do you confirm the deletion of the indicator?',
    caoZuoChengGong2: 'Successful operation!',
    zhiBiaoXiangQing: 'Indicator Details',
    shiShiDiaoYong: 'Realtime Call',
    liXianDiaoYong: 'Offline Call',
    zhiBiaoJiYongTu: 'Usage',
  },
  createversionmodal: {
    qingXuanZeLaiYuan: 'Select the source version',
    laiYuanBanBenBi: 'Source version required',
    xuanZeLaiYuanBan: 'Select source version',
    chuangJianZhiBiaoJi: 'Creating indicator set version',
    caoZuoChengGong: 'Successful operation',
    queDing: 'OK',
    quXiao: 'Cancel',
    chuangJianZhiBiaoBan: 'Creating indicator version',
  },
  indexpackagemodal: {
    qingShuRuZhiBiao: 'Enter a description of the indicator set',
    zhiBiaoJiMiaoShu: 'Indicator set description',
    xuanZeZhiBiaoJi: 'Selecting Indicator set main attributes',
    qingXuanZeZhiBiao: 'Select the main attribute of the indicator set',
    zhiBiaoJiZhuShu: 'Indicator set main attributes',
    ziMuShuZiXia: 'Alphanumeric underscore composition',
    zhiBiaoJiBiaoZhi: 'Indicator set markers up to a maximum of 50',
    qingTianXieZhiBiao: 'Fill in the indicator set ID',
    zhiBiaoJiBiaoZhi2: 'Indicator Set ID',
    qingShuRuZhiBiao2: 'Enter the name of the indicator set',
    zhiBiaoJiMingCheng: 'Maximum of 200 indicator set names',
    qingTianXieZhiBiao2: 'Fill in the name of the indicator set',
    zhiBiaoJiMingCheng2: 'Indicator Set Name',
    xuanZeBaoWen: 'Select Message',
    qingXuanZeJieXi: 'Select to parse the message',
    jieXiBaoWen: 'Parsed message',
    xuanZeQuDao: 'Select Channel',
    qingXuanZeQuDao: 'Select a channel',
    suoShuQuDao: 'Channel',
    tianJiaZhiBiaoJi: 'Add indicator set',
    xiuGaiZhiBiaoJi: 'Modify indicator set',
    quXiao: 'Cancel',
    chaKanZhiBiaoJi: 'View indicator set',
    xinZengChengGong: 'Added successfully',
    gengXinChengGong: 'Update successfully',
    xuanZeZhuShuXing: 'Select Master Attribute Mapping Fields',
    qingXuanZeZhuShu: 'Select a master attribute mapping field',
    zhuShuXingYingShe: 'Master Attribute Mapping Field',
    ruGuoZhiBiaoJi:
      'If the metrics set usage is to run batches offline, individual metrics can only be attributed to one metrics set, i.e. the metrics set relationship between metrics and offline tasks is 1:1',
    liXianDiaoYong: 'Offline Call',
    shiShiDiaoYong: 'Realtime Call',
    zhiBiaoJiYongTu: 'Indicator set usage',
  },
  indextable: {
    gongTOTA: 'Total {val1} items',
    tianJia: 'Add',
    chaXun: 'Query',
    zhiBiaoFenLei: 'Indicator category',
    zhiBiaoBiaoZhi: 'Indicator ID',
    zhiBiaoMingCheng: 'Indicator name',
    qingGouXuanPeiZhi: 'Select the configured indicator',
    caoZuoChengGong: 'The operation was successful.',
    shanChu: 'Delete',
    quXiao: 'Cancel',
    queRen: 'OK',
    shanChuQueRen: 'Delete Confirmation',
    chaKan: 'View',
    caoZuo: 'Action',
  },
  reportdetailmodal: {
    tiaoJiLu: 'records',
    gong: 'in total',
    xiaZai: 'Download',
    zhongZhi: 'Reset',
    souSuo: 'Search',
    zhiBiaoMingChengMo: 'Fuzzy search for indicator name',
    jinJianLiuShuiHao: 'Application serial number:',
    baoGaoXiangQing: 'Report details',
    zhiBiaoJieGuo: 'Indicator results',
    zhiBiaoBiaoZhi: 'Indicator ID',
    zhiBiaoMingCheng: 'Indicator name',
  },
  offlinemodal: {
    guanBi: 'Close',
    renWuZhuangTai: 'TaskStatus:',
    wuShuChuWenJian: 'No output file',
    shuChuWenJian: 'Output file:',
    shuRuWenJian: 'Input file:',
    kaiShiShiJian: 'Start time:',
    zhiBiaoJi: 'Indicator set:',
    renWuMingCheng: 'Task name:',
    xiangQing: 'Details:',
    riZhi: 'Log',
  },
  operationslist: {
    qingShuRuShaiXuan: 'Enter a filtered task name',
    xiaZai: 'Download',
    shanChu: 'Delete',
    quXiao: 'Cancel',
    queRen: 'OK',
    shanChuQueRen: 'Delete Confirmation',
    xiangQing: 'Details',
    riZhi: 'Log',
    shanChuChengGong: 'Deleted successfully',
    fen: 'Minute',
    xiaoShi: 'Hour',
    tian: 'Day',
    caoZuo: 'Action',
    renWuZhuangTai: 'Task status',
    dangQianJinDu: 'Current progress',
    yunXingShiJian: 'Run time',
    kaiShiShiJian: 'Start time',
    shuChuGeShi: 'Output format',
    baoWenGeShu: 'Number of messages',
    renWuMingCheng: 'Task name',
    chuangJianRen: 'Creator',
    xuHao: 'Serial number',
  },
  paramform: {
    queRenTiJiao: 'Confirm submit',
    biTian: 'Required',
    kaiShiRiQi: 'Start time',
    jieShuShiJian: 'End time',
    kaiShiShiJian: 'Start time',
    shiJianXuanZe: 'Date',
    jiHuaRenWu: 'Planned task',
    liJiRenWu: 'Immediate task',
    shiJianSheDing: 'Time setting',
    lie: 'Column',
    xing: 'Row',
    shuChuMoShi: 'Output mode',
    jieGuoShuChu: 'Result Output',
    qingShuRuRenWu: 'Enter a task name',
    renWuMingCheng: 'Task name',
    jiHuaSheDing: 'Program Setup',
    qingShuRuWenJian: 'Enter the file path',
    zhiZhiChiShangChuan: 'Support uploading zip files only',
    shangChuanWenJian: 'Uploading files',
    zhiDingWenJianLu: 'Specify the file path',
    wenJianShangChuan: 'File upload',
    qingXuanZeZhiBiao: 'Select an indicator set',
    qingXuanZeBaowen: 'Select message type',
    zhiBiaoJiSheDing: 'Indicator set setting',
    baoWenLeiXingXuan: 'Message type selection',
    baoWenGeShiXuan: 'Message format selection',
    baoWenShuRu: 'Message Input',
    iNFOF: '{val1} Upload failed',
    iNFOF2: '{val1} Successful upload',
    renWuTianJiaCheng: 'Task added successfully',
  },
  catalogmodal: {
    qingShuRuYuanShi: 'Enter the original path',
    qingTianXieYuanShi: 'Fill in the original path',
    yuanShiLuJing: 'Original path',
    qingShuRuZiDuan: 'Enter field ID',
    ziMuShuZiXia: 'Alphanumeric underscore composition',
    changDuBuNengChao:
      'Cannot exceed 200 in length and does not contain special characters',
    qingTianXieZiDuan: 'Fill in the field ID',
    ziDuanBiaoZhi: 'Field ID',
    qingXuanZeZiDuan: 'Select field type',
    ziDuanLeiXing: 'Field type',
    buNengChaoGuoGe: 'Cannot exceed 200 characters',
    buNengBaoHanTe: 'Cannot contain special characters',
    aZAZYi: '^[a-zA-Z0-9]+$',
    qingTianXieZiDuan2: 'Fill in the field name',
    ziDuanMingCheng: 'Field name',
    jieGouPeiZhi: 'Structure Configuration',
    baoCunChengGong: 'Saved successfully',
    yingYouZiMuShu: 'Should consist of letters, numbers, underscores',
    qingTianXieFenLei: 'Fill in the category ID',
    fenLeiBiaoZhi: 'Category ID',
    yingYouHanZiZi:
      'Should consist of Chinese characters, letters, numbers underlined',
    qingTianXieFenLei2: 'Fill in the name of the category',
    fenLeiMingCheng: 'Category name',
    fenLeiPeiZhi: 'Category Configuration',
    aZAZYi2: '^[a-zA-Z0-9_一-龥]+$',
  },
  classifyconfig: {
    gengXinChengGong: 'Update successfully',
    yingYouZiMuShu: 'Should consist of letters, numbers, underscores',
    qingTianXieFenLei: 'Fill in the category ID',
    fenLeiBiaoZhi: 'Category ID',
    buNengChaoGuoGe: 'Cannot exceed 200 characters',
    changDuBuNengChao:
      'Cannot exceed 200 in length and does not contain special characters',
    yingYouHanZiZi:
      'Should consist of Chinese characters, letters, numbers underlined',
    aZAZYi: '^[a-zA-Z0-9]+$',
    qingTianXieFenLei2: 'Fill in the name of the category',
    fenLeiMingCheng: 'Category name',
    fenLeiPeiZhi: 'Category Configuration',
    baoCunChengGong: 'Save Successful',
    baoCun: 'Save',
    zhongZhi: 'Reset',
    zhiBiaoMuBanFen: 'Indicator template category',
    zhiBiaoFenLei: 'Indicator category',
    leiXing: 'Type',
    qingShuRuZiDuan: 'Enter field ID',
    baoWenBiaoZhi: 'Message ID',
    baoWenMingCheng: 'Template name',
    zhanKai: 'Unfold',
    shouQi: 'Fold',
    tianJiaFenLei: 'Add Category',
    qingXuanZeFenLei: 'Select a category type',
    souSuoFenLeiMing: 'Search Category Name',
    shanChu: 'Delete',
    tianJia: 'Add',
    shanChuChengGong: 'Deleted successfully',
    quXiao: 'Cancel',
    queRen: 'OK',
    wenXinTiShiFen:
      'Warm tip: After the category is deleted, it can not be restored',
    queDingShanChuCi: 'Sure you want to delete this structure?',
    wenXinTiShiGai:
      'Tips: This operation will delete all the structure under the structure, after deletion can not be restored!',
    zuoCeXuanZeXiang: 'Select the corresponding field on the left',
    fenLeiSheZhi: 'Category setting',
    baoWenGuanLi: 'Message management',
    aZAZYi2: '^[a-zA-Z0-9_一-龥]+$',
  },
  detailconfig: {
    zuoCeXuanZeXiang: 'Select the corresponding field on the left',
    jieGouSheZhi: 'Structure setting',
    baoWenGuanLi: 'Message management',
  },
  enumform: {
    xianShiZhi: 'Displayed value',
    yuanShiZhi: 'Original value',
  },
  importmodal: {
    xiangTongZhongDuanDao: 'Same interrupt import',
    fuGaiMoShiHui:
      'Overwrite mode overwrites message samples with null, so please be careful!',
    xiangTongFuGai: 'Same coverage',
    xiangTongTiaoGuo: 'Ditto skip',
    qingXuanZeXiangTong: 'Select the same indicator to operate',
    xiangTongBaoWenCao: 'Identical Message Operation',
    shangChuanWenJian: 'Upload file',
    qingShangChuanDO: 'Upload a .document file.',
    xuanZeWenJian: 'Select File',
    baoWenDaoRu: 'Message Import',
    iNFOF: '{val1} Upload failed',
    iNFOF2: '{val1} Successful upload',
    shangChuanWenJianBu: 'Uploaded file cannot exceed 10MB!',
    yiKaiQiQieBei:
      'Enabled and referenced indicators are automatically skipped and not overwritten',
    xiangTongZhiBiaoCao: 'Same indicator operation',
    jieXiWenJianHuo:
      'Parses the file to obtain the message id and imports the indicator under the relevant message structure. If the message id does not exist, the import fails.',
    qingShangChuanIN: 'Upload the .index file',
    qingXuanZe: 'Select',
    xuanZeQuDao: 'Select channel',
    zhiBiaoDaoRu: 'Indicator import',
  },
  list: {
    daoChu: 'Export',
    baoWenCEF: 'Message.cef',
    daoRu: 'Import',
    souSuoName: 'Message Name',
    xinZeng: 'Add',
    chaXun: 'Query',
    qingShuRu: 'Enter',
    baoWenGuanLi: 'Message management',
    baoWenBiaoZhi: 'Identification',
    baoWenMingCheng: 'Name',
    caoZuo: 'Action',
    xiuGaiRen: 'Modifier',
    xiuGaiShiJian: 'Modification date',
    chuangJianRen: 'Creator',
    chuangJianShiJian: 'Creat time',
    shanChu: 'Delete',
    quXiao: 'Cancel',
    queRen: 'OK',
    shanChuQueRen: 'Delete Confirmation',
    fenLeiPeiZhi: 'Category Configuration',
    jieGouPeiZhi: 'Structure Configuration',
    xiuGai: 'Modify',
    chaKan: 'View',
    shanChuChengGong: 'Deleted successfully!',
    quanXuan: 'All',
    piLiangXiaXian: 'Batch Offline',
    duiYingZhiBiaoTong: 'Corresponding indicators synchronized offline',
    piLiangXiaXianCheng: 'Batch offline success',
    caoZuoChengGong: 'The operation was successful',
    piLiangShangXian: 'Batch Online',
    piLiangShangXianCheng: 'The batch went live successfully!',
    sanFangMuBanI: 'ExternalServiceTemplate.idxtmp',
    qingXuanZeXuYao: 'Select the data to be exported',
    muBanMingCheng: 'Name',
    zhiBiaoMuBan: 'Indicator Template',
    xiaXian: 'Offline',
    shangXian: 'Online',
    zhuangTai: 'Status',
    baoWenJieGou: 'Message structure',
    muBanGuanLi: 'Template Management',
    ziDingYiMuBan: 'Customized Template',
    jiChuMuBan: 'Basic Template',
    muBanLeiXing: 'Type',
    muBanFenLei: 'Category',
    muBanBiaoZhi: 'Identification',
    kaoBei: 'Copy',
  },
  paramsmodel: {
    qingShuRuNeiRong: 'Enter a sample of content, maximum 2MB',
    baoWenShiLi: 'Sample Message',
    baoWenChongXinDao:
      'The structure needs to be changed manually after the message is reimported',
    daoRu: 'Import',
    qingShangChuanWenJian:
      'Upload a file with the same suffix as the selected message type.',
    baoWenDaoRu: 'Message Import',
    ziMuShuZiXia: 'Alphanumeric underscore composition',
    qingTianXieBaoWen: 'Fill in the message ID',
    baoWenBiaoZhi: 'message ID',
    buNengChaoGuoGe: 'Cannot exceed 200 characters',
    baoWenMingChengChang: 'The length of the Template name cannot exceed 200',
    qingTianXieBaoWen2: 'Fill in the name of the message',
    baoWenMingCheng: 'Template name',
    qingXuanZeBaoWen: 'Select the message type',
    baoWenLeiXing: 'message type',
    tianJiaBaoWen: 'Add Message',
    xiuGaiBaoWen: 'Modify Message',
    quXiao: 'Cancel',
    chaKanBaoWen: 'View Message',
    xinZengChengGong: 'Added successfully',
    gengXinChengGong: 'Update successfully',
    baoWenShiLiYong:
      'The message example is used to automatically generate the initial message structure configuration after saving. If there is no available message example, you can enter { } as the default json message structure.',
    baoWenShiLiYong2:
      'The message example is used to automatically generate the initial Message structure configuration after saving. If there is no available message example, you can enter <xml></xml> as the default xml Message structure.',
  },
  searchtree: {
    tianJia: 'Add',
    souSuoZiDuanMing: 'Search Field Name',
    shanChu: 'Delete',
    xiuGai: 'Modify',
    shanChuChengGong: 'Deleted successfully',
    quXiao: 'Cancel',
    queRen: 'OK',
    wenXinTiShiZi: 'Tip: fields cannot be restored after deletion',
    queDingShanChuCi: 'Sure you want to delete this structure?',
    wenXinTiShiGai:
      'Tips: This operation will delete all the structure under the structure, after deletion can not be restored!',
    zhanKai: 'Unfold',
    shouQi: 'put away',
    tianJiaFenLei: 'Add Category',
    souSuoFenLeiMing: 'Search Category Name',
    wenXinTiShiFen:
      'Warm tip: After the category is deleted, it can not be restored',
  },
  treenodeitem: {
    baoCun: 'Save',
    zhongZhi: 'Reset',
    qingShuRuZhiBiao: 'Enter a description of the indicator',
    ziDuanMiaoShu: 'Field Description',
    qingShuRuYuanShi: 'Enter the original path',
    qingTianXieYuanShi: 'Fill in the original path',
    yuanShiLuJing: 'original path',
    qingShuRuBaoWen: 'Enter the message path',
    qingTianXieBaoWen: 'Fill in the message path',
    baoWenLuJing: 'message path',
    qingTianXieMeiJu: 'Fill in the Enumeration settings',
    meiJuSheZhi: 'Enumeration settings',
    shiLiYYY: 'Example: yyyy-MM-dd',
    qingXuanZeRiQi: 'Select date format',
    riQiGeShi: 'date format',
    dongTai: 'dynamic',
    jingTai: 'static',
    xuanZeZiDuanShu: 'Selecting Field Properties',
    qingXuanZeZiDuan: 'Select field properties',
    shuXingBiaoZhu: 'Attribute labeling',
    qingXuanZeZiDuan2: 'Select field type',
    ziDuanLeiXing: 'Field type',
    qingShuRuZiDuan: 'Enter field ID',
    changDuBuNengChao: 'Length cannot exceed 200',
    qingTianXieZiDuan: 'Fill in the field ID',
    ziDuanBiaoZhi: 'field ID',
    buNengChaoGuoGe: 'Cannot exceed 200 characters',
    buNengBaoHanTe: 'Cannot contain special characters',
    aZAZYi: '^[a-zA-Z0-9]+$',
    qingTianXieZiDuan2: 'Fill in the field name',
    ziDuanMingCheng: 'field name',
    gengXinChengGong: 'Update successfully',
    aZAZYi2: '^[a-zA-Z0-9_一-龥]+$',
  },
  addpackagemodal: {
    xuanZeZhiBiaoJi: 'Selection of indicator sets',
    qingXuanZeZhiBiao: 'Select an indicator set',
    zhiBiaoJi: 'indicator set',
    yiXiaXianZhiBiao:
      '*Offline indicators do not run after being added to the indicator set',
    suoShuZhiBiaoJi: 'Indicator Set',
    jiaRuChengGong: 'Add Success',
  },
  batchtemplate: {
    tianJia: 'Add',
    tiaoJian: 'condition',
    caoZuo: 'Action',
    tiaoJianXianShiMing: 'Condition Display Name',
    tiaoJianPeiZhi: 'Condition Configuration',
    quanBu: 'All',
  },
  bulktemplatemodal: {
    zhiBiaoLuoJi: 'Indicator logic',
    qingShuRuMoRen: 'Enter the default value',
    moRenZhi: 'Default value',
    qingXuanZeQuDao: 'Select a channel',
    quDao: 'Channel',
    qingXuanZeZhiBiao: 'Select an indicator template',
    zhiBiaoMuBan: 'Indicator template',
    qingShuRuZhiBiao: 'Enter the indicator ID prefix',
    ziMuShuZiXia: 'Alphanumeric underscore composition',
    zhiBiaoQianZhui: 'Indicator prefix',
    qingXuanZeLeiXing: 'Select type',
    shuChuZhiLeiXing: 'Output value type',
    qingXuanZeZhiBiao2: 'Select an indicator category',
    zhiBiaoFenLei: 'Indicator category',
    xuanZeBaoWen: 'Select Message',
    qingXuanZeJieXi: 'Select to parse the message',
    jieXiBaoWen: 'Parsed message',
    piLiangZhiBiao: 'Batch Indicator',
    zhiBiaoLuoJiTiao:
      'The value of the text input in the logical condition of the indicator does not match the regularity',
    zhiBiaoLuoJiBu: 'Incorrect indicator logic',
    kePeiZhiCanShu: 'Insufficient configurable parameters for batch creation',
    fenLei: 'Category',
    muBan: 'Template',
    xinZengChengGong: 'Added successfully',
  },
  createmenus: {
    ziDingYiZhiBiao: 'Customize indicator',
    quZhiZhiBiao: 'Taking value Indicator',
    muBanZhiBiao: 'Template indicator',
  },
  diyindexmodal: {
    fuZhi: 'Copy',
    jiSuanJiaoBen: 'Calculation script',
    qingXuanZeMuBan: 'Select a template type',
    muBanLeiXing: 'Template Type',
    ruoXuJiSuanGuo:
      'If it is necessary to calculate the last six months, amend "-12" to "-6" and give details in the notes.',
    canShuShuoMingGuo: 'Parameter description: Past 12 months.',
    canShuZhi: 'Parameter value: "-12".',
    canShuMingMO:
      'Parameter name: monthNum (actually based on the outgoing parameter field).',
    yiGuoQuGeYue:
      'Take "Maximum number of credit card M1s for applicants in the past 12 months" as an example.',
    canShuSheZhiFang:
      "Parameter settings to facilitate quick assignment of values to indicator calculation variables; if the parameter name starts with index_ then the parameter value is filled in with the indicator's ID.",
    canShu: 'Parameter',
    qingShuRuMoRen: 'Enter the default value',
    moRenZhi: 'Default value',
    qingShuRuZhiBiao: 'Enter a description of the indicator',
    zhiBiaoMiaoShu: 'Description of indicators',
    qingXuanZeLeiXing: 'Select type',
    shuChuZhiLeiXing: 'Output value type',
    qingXuanZeZhiBiao: 'Select an indicator category',
    zhiBiaoFenLei: 'Indicator category',
    xuanZeBaoWen: 'Select Message',
    qingXuanZeJieXi: 'Select to parse the message',
    jieXiBaoWen: 'Parsed message',
    ziMuShuZiXia: 'Alphanumeric underscore composition',
    qingTianXieZhiBiao: 'Fill in the indicator ID',
    zhiBiaoBiaoZhi: 'Indicator ID',
    xiTongNeiZhi: 'Built in system',
    qingXuanZeQuDao: 'Select a channel',
    quDao: 'Channel',
    qingShuRuZhiBiao2: 'Enter the name of the indicator',
    jinZhiChiZhongYing:
      'Supports only English, Chinese, numbers, and underscores.',
    zhiBiaoMingCheng: 'Indicator name',
    fuZhiZhiBiao: 'Copy Indicator',
    tianJiaZhiBiao: 'Add Indicator',
    xiuGaiZhiBiao: 'Modify Indicator',
    quXiao: 'Cancel',
    chaKanZhiBiao: 'View Indicator',
    zhanWeiCanShuDou: 'None of the placeholder parameters can be null!',
    qingShuRuPY: 'Enter a Python format template',
    qingShuRuJA: 'Enter a Java format template',
    buFuHeJS: 'Does not conform to JSON format!',
    geShiHuaChengGong: 'Formatting was successful!',
    fuZhiChengGong: 'Copy successfully',
    xinZengChengGong: 'Added successfully',
    gengXinChengGong: 'Update successfully',
  },
  modifymodal: {
    qingTianXieMiaoShu: 'Fill in the description',
    miaoShu: 'Description',
    qingXuanZeZhiBiao: 'Select an indicator category',
    fenLei: 'Category',
    qingTianXieMingCheng: 'Fill in the name',
    mingCheng: 'Name',
    bianJiZhiBiao: 'Edit Indicator',
    caoZuoChengGong: 'The operation was successful',
    queDing: 'OK',
    quXiao: 'Cancel',
  },
  sqlparams: {
    canShuShuoMing: 'Parameter description',
    canShuZhi: 'parameter value',
    canShuMing: 'parameter name',
  },
  templatemodal: {
    muBanPeiZhi: 'Template Configuration',
    qingXuanZeZhiBiao: 'Select an indicator template',
    zhiBiaoMuBan: 'Indicator template',
    qingShuRuMoRen: 'Enter the default value',
    moRenZhi: 'Default value',
    qingShuRuZhiBiao: 'Enter indicator description',
    zhiBiaoMiaoShu: 'Indicator Description',
    qingXuanZeLeiXing: 'Select type',
    shuChuZhiLeiXing: 'Output value type',
    qingXuanZeZhiBiao2: 'Select an indicator category',
    zhiBiaoFenLei: 'Indicator category',
    xuanZeBaoWen: 'Select Message',
    qingXuanZeJieXi: 'Select to parse the message',
    jieXiBaoWen: 'Parsed message',
    ziMuShuZiXia: 'Alphanumeric underscore composition',
    qingTianXieZhiBiao: 'Fill in the indicator ID',
    zhiBiaoBiaoZhi: 'Indicator ID',
    xiTongNeiZhi: 'Built in system',
    qingXuanZeQuDao: 'Select a channel',
    quDao: 'Channel',
    qingShuRuZhiBiao2: 'Enter indicator name',
    jinZhiChiZhongYing:
      'Supports only English, Chinese, numbers, and underscores',
    zhiBiaoMingCheng: 'Indicator name',
    fuZhiZhiBiao: 'Copy Indicator',
    tianJiaZhiBiao: 'Add Indicator',
    xiuGaiZhiBiao: 'Modify Indicator',
    quXiao: 'Cancel',
    chaKanZhiBiao: 'View Indicator',
    wenBenShuRuDe: 'The value of the text input does not match the regular',
    muBanPeiZhiBu: 'Incorrect template configuration',
    qingXuanZeDA: 'Select a template of type {val1}.',
    qingXianXuanZeShu: 'Select the output value type first',
    shiJian: 'Date',
    buEr: 'Boolean',
    ziFuChuan: 'String',
    fuDianShu: 'Float',
    zhengShu: 'Integer',
    fenLei: 'Category',
    muBan: 'Template',
    xinZengChengGong: 'Added successfully',
    gengXinChengGong: 'Update successfully',
  },
  testmodel: {
    yunXingJieGuo: 'Running result',
    yunXingShiBai: 'Failed to run',
    yunXingZhuangTai: 'Runtime status',
    yunXingChengGong: 'Run successfully',
    qingShuRuJS: 'Enter JSON, XML, STRING format',
    baoWen: 'Message',
    ceShi: 'Test',
    ceShiZhiBiao: 'Test metrics',
  },
  vartemplatemodal: {
    caoZuoChengGong: 'The operation was successful.',
    xuHao: 'Serial number',
    qingXuanZeQuZhi: 'Select the value configuration',
    qingXuanZeQuZhi2: 'Select the value configuration',
    quZhiPeiZhi: 'Value Configuration',
    qingShuRuZhiBiao: 'Enter a description of the indicator',
    zhiBiaoMiaoShu: 'Description of indicators',
    qingShuRuMoRen: 'Enter the default value',
    moRenZhi: 'Default value',
    qingXuanZeZhiBiao: 'Select an indicator category',
    zhiBiaoFenLei: 'Indicator category',
    xuanZeBaoWen: 'Select Message',
    qingXuanZeJieXi: 'Select to parse the message',
    jieXiBaoWen: 'Parsed message',
    ziMuShuZiXia: 'Alphanumeric underscore composition',
    qingTianXieZhiBiao: 'Fill in the indicator ID',
    zhiBiaoBiaoZhi: 'Indicator ID',
    xiTongNeiZhi: 'Built in system',
    qingXuanZeQuDao: 'Select a channel',
    quDao: 'Channel',
    qingShuRuZhiBiao2: 'Enter the name of the indicator',
    jinZhiChiZhongYing:
      'Supports only English, Chinese, numbers, and underscores.',
    zhiBiaoMingCheng: 'Indicator name',
    fuZhiZhiBiao: 'Copy Indicator',
    tianJiaZhiBiao: 'Add Indicator',
    xiuGaiZhiBiao: 'Modify Indicator',
    quXiao: 'Cancel',
    chaKanZhiBiao: 'View Indicator',
    xinZengChengGong: 'Added successfully',
    gengXinChengGong: 'Update successfully',
    shiFouCunZai: 'whether or not',
    tiaoShu: 'Item count',
    quZhiZhiBiaoXiu:
      'Field types are not allowed to be modified when the value indicator is modified.',
    qingShuRuXuHao: 'Please enter the serial number',
  },
  basedetail: {
    congITEM: 'Drag from {val1} to {val2}.',
    quShiLei: 'Trends',
    shiJianLei: 'Time class',
    jiSuanLei: 'Computing class',
    tongJiLei: 'Statistical class',
    jieGuoSuanZiZu: 'Resultant Operator Component Library',
    leiBieKu: 'Category library',
    shuZhiKu: 'Numerical library',
    ziFuChuanKu: 'String library',
    shiTiBiaoZhiKu: 'Entity ID library',
    tiaoJianShaiXuanZu: 'Conditional Filtering Component Library',
    shiJianDaoXuLei: 'Reverse time class',
    shiJianFanWeiLei: 'Time range class',
    shiJianShaiXuanZu: 'Time Filter Library',
    beiBaoHanYu: 'included in',
    buBaoHan: 'exclude',
    baoHan: 'include',
    nian: 'Year',
    hanBenYue: 'Including this month',
    ziRanYue: 'Natural month',
    yue: 'Month',
    tian: 'Day',
    xiaoYuDengYu: 'less than or equal to',
    xiaoYu: 'less than',
    dengYu: 'equal',
    daYuDengYu: 'greater than or equal to',
    daYu: 'greater than ',
    qingTuoDongZuJian: 'Drag components to this grouping',
    tiaoJianShaiXuan: 'Condition Filtering',
    xinZengTiaoJianShai: 'New Conditional Filter Grouping',
    qie: 'and',
    huo: 'or',
    zuNeiGuanXi: 'Within group relationship',
    ziDingYiCanShu: 'Custom parameters drop down box',
    qingXuanZe: 'Select',
    xinZengSuanZi: 'Add operator',
    xinZengSuanZi2: '+ add operator',
    suanZiShaiXuan: 'Operator Screening',
    suanZiGongShi: 'Operator formula',
    qingShuRuSuanZi: 'Enter the name of the operator, it cannot be repeated',
    suanZiMingCheng: 'Algorithm name',
    suanZiMingChengBu: 'The name of the operator cannot be repeated!',
    deShiJianCha: 'The time difference',
    ziDingYiSuanZi: 'Customizable operator',
    shiJianShaiXuan: 'Time Screening',
    shuRuKuang: 'Input',
    zhi: 'Until',
    de: 'the',
    zai: 'Exist',
    shuXingPeiZhi: 'Property Configuration',
    xiuGai: 'Modify',
    chaKan: 'View',
    xinJian: 'Add',
    kaoBei: 'Copy',
    muBanGuanLi: 'Back',
    baoCun: 'Save',
    yiDuanMiaoShuWen: 'A Description piece of copy',
    muBanMiaoShu: 'Template Description',
    qingXuanZeMuBan: 'Select a template category',
    muBanFenLei: 'Template Category',
    muBanBiaoZhi: 'Template ID',
    muBanMingCheng: 'Template Name',
    muBanShengCheng: 'Template Generation',
    xinZengChengGong: 'Added successfully',
    gengXinChengGong: 'Update successfully',
    qingPeiZhiZiDing: 'Configure custom parameters!',
    yu: 'and',
    qie2: ',and',
    deNGeZhiN: 'of [N1] to [N2]',
    nGe: '[N]',
    jian: 'Subtraction',
    xuanZhiQuJian: 'Selection Range:',
    shuZhiMingCheng: 'Numeric Name:',
    ziDingYiShuZhi: 'Customized Numeric Items',
    moRenZhi: 'Default Value',
    ziDingYiShuZhi2: '+ customized numeric entries',
    ziDingYi: 'Customizable',
    quanBu: 'All',
    shuJuZhi: 'Data Value',
    duoXuanXiaLaKuang: 'Multi-select drop-down box',
    danXuanXiaLaKuang: 'Radio drop-down box',
    zuJianLeiXing: 'Component Type:',
    weiYiBiaoZhi: 'Unique ID :',
    huo2: ',or',
  },
  detail: {
    baoCun: 'Save',
    ceShi: 'Test',
    quXiao: 'Cancel',
    jiSuanJiaoBen: 'Calculation script',
    qingXuanZeMuBan: 'Select a template type',
    muBanLeiXing: 'Template Type',
    qingPeiZhiMuBan: 'Configure the template',
    muBanPeiZhi: 'Template Configuration',
    qingShuRuZhiBiao: 'Enter a description of the indicator',
    muBanMiaoShu: 'Template Description',
    qingXuanZeFanHui: 'Select the return value type',
    fanHuiZhiLeiXing: 'Return value type',
    qingXuanZeMuBan2: 'Select a template category',
    muBanFenLei: 'Template Category',
    ziMuShuZiXia: 'Alphanumeric underscore composition',
    qingTianXieMuBan: 'Fill in the template ID',
    muBanBiaoZhi: 'Template ID',
    qingShuRuMuBan: 'Enter a template name',
    qingTianXieMuBan2: 'Fill in the name of the template',
    muBanMingCheng: 'Template name',
    jiBenXinXi: 'Basic Information',
    xiuGai: 'Modify',
    xinJian: 'Add',
    kaoBei: 'Copy',
    muBanGuanLi: 'Template Management',
    fuZhiChengGong: 'Copy successfully',
    wenBenShuRuDe: 'The value of the text input does not match the regular',
    qingShuRuPY: 'Enter a PYTHON format template',
    qingShuRuJA: 'Enter a Java format template',
    xinZengChengGong: 'Add successfully',
    gengXinChengGong: 'Update successfully',
    kaoBeiChengGong: 'Copy successfully',
    kongJianLeiXingYing:
      'The template configuration should start with a text box component, and the control type should include at least one of text box, number box, single selection drop-down box, or multiple selection drop-down box.',
    shiJian: 'Date',
    buEr: 'Boolean',
    ziFuChuan: 'String',
    fuDianShu: 'Float',
    zhengShu: 'Integer',
  },
  detailview: {
    baoCun: 'Save',
    quXiao: 'Cancel',
    jiSuanJiaoBen: 'Calculation script',
    muBanLeiXing: 'Template type',
    muBanPeiZhi: 'Template donfiguration',
    muBanMiaoShu: 'Template description',
    muBanFenLei: 'Template category',
    fanHuiZhiLeiXing: 'Return value type',
    muBanBiaoZhi: 'Template ID',
    muBanMingCheng: 'Template name',
    jiBenXinXi: 'Basic Information',
    chaKan: 'View',
    muBanGuanLi: 'Template Management',
    fuZhiChengGong: 'Copy successfully',
  },
  testmodal: {
    yunXingJieGuo: 'Running result',
    yunXingShiBai: 'Failed to run',
    yunXingZhuangTai: 'Runtime status',
    yunXingChengGong: 'Run successfully',
    qingShuRuJS: 'Enter JSON, XML format, STRING format',
    baoWen: 'Message',
    muBanPeiZhi: 'Template Configuration',
    ceShi: 'Test',
    wenBenShuRuDe: 'The value of the text input does not match the regular',
    muBanPeiZhiBu: 'Incorrect template configuration',
  },
};
