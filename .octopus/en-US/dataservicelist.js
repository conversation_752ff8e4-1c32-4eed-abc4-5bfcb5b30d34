export default {
  daoRuChengGong: 'Imported successfully',
  tiaoJiLu: 'Totally {val1} Record(s)',
  gong: 'Total',
  xin<PERSON><PERSON>: 'Add',
  daoRu: 'Import',
  cha<PERSON>un: 'Query',
  zan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'No permission',
  xia<PERSON><PERSON>: 'Go offline',
  shang<PERSON><PERSON>: 'Go online',
  qing<PERSON>uanZe<PERSON>ie<PERSON>: 'Select status',
  yiBu<PERSON>ie<PERSON><PERSON>: 'Asynchronous interface',
  tongBuJie<PERSON>ou: 'Synchronization interface',
  qingXuanZeJieKou2: 'Select type',
  qingXuanZeShuJu: 'Select dataType',
  qingXuanZeShuJu2: 'Select Interface',
  qingXuanZeHeZuo: 'Select a partner',
  gengDuo: 'More',
  xiangQing: 'Detail',
  xiuGai: 'Modify',
  yinYongGuanXi: 'Reference relation',
  shanChu: 'Delete',
  daoChu: 'Export',
  mOCKPei: 'Mock',
  fu<PERSON>hi: 'Copy',
  ce<PERSON>hi: 'Test',
  cao<PERSON><PERSON>: 'Action',
  xiuGai<PERSON>en: 'Modifier',
  xiu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'Modification time',
  chuang<PERSON>ian<PERSON><PERSON>: 'Creator',
  chuangJianShiJian: 'Creation time',
  tEXTTian: '{val1} days',
  kongZhiDiaoYongGuo:
    'Control whether to save the result data during the call process, such as saving, and set the saving cycle',
  huanCunYouXiaoQi: 'Cache validity period',
  yiXiaXian: 'Offline',
  yiShangXian: 'Online',
  yiShanChu: 'Deleted',
  zhuangTai: 'Status',
  jieKouLeiXing: 'Interface type',
  heZuoFangMingCheng: 'Partner name',
  shuJuLeiXing: 'Data type',
  shuJuYuanFuWu: 'Data source service interface id',
  shuJuYuanFuWu2: 'Data source service interface name',
  daoChuShiBai: 'Export failed!',
  cunZaiQiangYinYong:
    'Strong references (referenced by online, enabled, and other related state components) exist, prohibiting the operation of the',
  queDingShanChuCi: 'Confirm to delete this data?',
  caoZuoChengGong: 'Operation successful',
  qingShuRuFuWu: 'Enter interface id',
  jieGouHuaPeiZhi: 'Structure Configuration',
  baoWenJieGou: 'Message Structure',
  xinZengChuCuo: 'Error',
  tianJiaChengGong: 'Successful',
  muBiaoJieDianBuCunZai: 'Error: Node not found',
  gengXin: 'Update',
  quXiao: 'Cancel',
  queDing: 'OK',
  geiZiDuanBiaoShi: 'Error: Field ID already exists, unable to write data.',
  zhuangTaiBianGeng: 'Status update successful!',
  zhuangTaiTiXing: 'Status reminder',
  weiDaoZhiDingShiJian:
    'The current time has not reached the specified effective moment, are you sure you want to activate in advance?',
  zhuangTaiWeiKaiQi: 'The current status is opened, deletion is not allowed.',
  qiYongChengGong: 'Opened successfully',
  baoWenShanChuHou:
    'After deleting the message structure, the predetermined effective time will no longer apply.',
  queRenShanChu: 'Delete this message structure?',
  jiXuShanChu: 'OK',
  shanChuChengGong: 'Deletion successful',
  gengXinShiJian: 'Updated time',
  chongJian: 'Reconfiguration',
  chongJianChengGong: 'Successfully reconfigured',
  chongJianBaoWen: 'Message reconfiguration',
  chongXinDaoRuJiangShanChu:
    'Importing a new message structure will overwrite the original message structure.',
  shuJuBiaoMing: 'Data table',
  biaoZiDuanMing: 'Data table field',
  yuanBaoWenBianLiang: 'Original message variable',
  qingXuanZeBiaoShuJuLeiXing: 'Please select a type',
  ziDingYiBaoWenBianLiang: 'Custom message variable',
  suiJiShu: 'Random number',
  gengXinNeiRong: 'Effective time of update content',
  liJiShengXiao: 'Immediate effect',
  zhiDingShengXiaoShiJian: 'Desired effective time',
  xuanZeShiJian: 'Time selection',
  jieKouZhuangTai: 'Interface Status',
  fuWuJieKouBiao: 'Service Interface ID',
};
