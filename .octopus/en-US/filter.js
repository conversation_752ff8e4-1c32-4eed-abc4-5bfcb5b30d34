export default {
  index: {
    baoCun: 'Save',
    quXiao: 'Cancel',
    jieDianMingCheng: 'Node name',
    zaiShuJuGuoLu:
      '2. Configure filtering conditions on the edge where the data filtering component is connected to other components.',
    tuoDongZuJianYu:
      '1. Drag component to connect to the data filtering component, where the data filtering component is the upstream node',
    shuJuGuoLuZu:
      'The data filtering component needs to configure the conditions on the edge (as shown in the following figure). You can: ',
    shan<PERSON>huCiTiaoJian: 'Delete',
    tianJiaTiaoJian: 'Add condition',
    qingShuRu: 'Enter',
    qingXuanZe: 'Select',
    tianNei: 'day(s)',
    jin: 'Last',
    zeng<PERSON>iaTiaoJian: 'Add conditions',
    zengJiaTiaoJianZu: 'Add condition group',
    tianJiaTiaoJianZu: 'Add condition group',
    canShuZhiChangDu: 'The parameter value length cannot exceed',
    zhixingtiaojianceshi: 'Execution condition test',
    qingxuanzebiaoziduan: 'Please select a table field',
    qingshur<PERSON><PERSON><PERSON><PERSON>n: 'Please enter table fields',
    qingxuan<PERSON>tia<PERSON>ji<PERSON>: 'Please select conditions',
    bianliang: 'variant',
    changliang: 'Constant',
    qingxuanzexitongziduan: 'Please select a system field',
    yes: 'Yes',
    no: 'No',
    biaoZiDuanMingJin:
      'The table field name only supports lowercase letters, numbers, and underscores, and must start with a letter.',
  },
};
