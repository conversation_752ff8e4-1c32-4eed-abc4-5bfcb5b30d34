export default {
  selecttable: {
    xuanZeBiao: 'Select the table',
    weiZhuCe: 'Unregistered',
    yiZhuCe: 'Registered',
    biaoMingCheng: 'Table Name',
  },
  sqljob: {
    ceShiZhong: 'Testing',
    chuangJianFenQuBiao:
      "\"-- Create a partitioned table\ncreate table default.tab_test_dt (\nid int comment 'id',\nname string comment 'name')\ncomment 'Table of All Data Types'\npartitioned by (\nds string comment 'Partition Field'\n);\n-- Create a non-partitioned table\ncreate table default.tab_test_dt (\nid int comment 'id',\nname string comment 'name')\ncomment 'Table of All Data Types';\n-- Insert data into the partitioned table\ninsert into salaxy.tab_test_dt partition(ds='201709') values(1,'wangwu');\n-- Insert data into the non-partitioned table\ninsert into salaxy.tab_test_dt values(1,'wangwu');\n-- Query statement\nselect * from default.tab_test_dt limit 10;\n-- Query statement (using parameters)\nselect * from default.tab_test_dt where ds = [yyyyMMdd-1d])\"",
  },
  tablesource: {
    xuanZeBiao: 'Select the table',
    fenQu: 'Partition',
  },
  index: {
    qingXianXuanZeShu: 'Please select the data source first',
  },
};
