export default {
  historydialog: {
    zanWuLiShiDui: 'There is currently no conversation history.',
    jin<PERSON>ian<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'Displaying the last 30 conversations only.',
    zui<PERSON><PERSON><PERSON><PERSON>: 'Recent 30 days',
    zu<PERSON><PERSON><PERSON>: 'Yesterday',
    jin<PERSON><PERSON>: 'Today',
    sou<PERSON>uo: 'Search',
    souSuoLiShiDui: 'Search chat history',
    liShi<PERSON>uiHua: 'Chat history',
    weiMingMingDuiHua: 'Untitled Conversation',
  },
  index: {
    xinDuiHua: 'New Conversation',
    aIZhu<PERSON><PERSON>: 'AI assistant',
    huo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'Failed to retrieve suggested questions:',
    fenXiShiBai: 'Analysis failed',
    cha<PERSON><PERSON>: ') View',
    yiJianShengChengCheng:
      'Successfully generated with one click. You can view it on the corresponding [Rule Library page](/noah/ruleBase?ruleName=',
    yiJianShengChengCheng2:
      'Successfully generated with one click. You can view it on the corresponding [Rule Set page](/noah/ruleBase?ruleName=',
    huoQu<PERSON>iaoTian<PERSON>ie: 'Unable to load chat list:',
  },
  constants: {
    jiaoYiXiTongCao: 'Transaction System FAQ',
    heiChanQingBao: 'Threat Intelligence',
    zhengCeJieDu: 'Compliance Guide',
    jiaoYiFengKongWen: 'Transaction Risk FAQ ',
  },
};
